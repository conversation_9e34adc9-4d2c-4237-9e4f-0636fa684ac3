import { Input, Select, Radio, InputNumber, ColorPicker, Switch } from 'antd';
import { useRef } from 'react';

import { FieldType } from './constant';

import './field.less';
import { AggregationColor } from 'antd/es/color-picker/color';

export const getFormItem = (
  type: FieldType,
  props: Record<string, any>,
): React.ReactNode => {
  console.log(props, 'props');
  switch (type) {
    case FieldType.INPUT:
      return <Input onCompositionEnd={() => console.log('compend')} />;
    case FieldType.INPUT_NUMBER:
      return <InputNumber {...props} />;
    case FieldType.RADIO_GROUP:
      return <Radio.Group block optionType="button" buttonStyle="solid" {...props} />;
    case FieldType.TEXTAREA:
      return <Input.TextArea {...props} />;
    case FieldType.SELECT:
      return <Select {...props} />;
    case FieldType.COLOR:
      return <ColorPicker format="hex" {...props} />;
    case FieldType.SWITCH:
      return <Switch {...props} />;
    default:
      return null;
  }
};

interface FieldItemProps {
  fieldConfig: {
    type: FieldType;
    props: Record<string, any>;
  };
  onChange: (value: any) => void;
}

export const FieldItem = (props: FieldItemProps) => {
  const { fieldConfig, onChange } = props;
  const { type, props: fieldProps } = fieldConfig;
  const composingRef = useRef(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const onItemChange = (e: any) => {
    console.log(
      e?.target.value,
      e?.nativeEvent?.isComposing,
      composingRef.current,
      'onchange',
    );
    // 使用 composingRef 而不是 isComposing，因为 isComposing 可能不可靠
    if (composingRef.current) return;

    if (e instanceof AggregationColor) {
      onChange(e.toHexString());
    } else if (e?.target?.value !== undefined) {
      onChange(e?.target.value);
    } else onChange(e);
  };

  const onCompStart = () => {
    console.log('comp start');
    composingRef.current = true;
  };

  const onCompEnd = (e: any) => {
    console.log('comp end', e?.target?.value);
    composingRef.current = false;
    // 在合成结束时提交最终值
    if (e?.target?.value !== undefined) {
      onChange(e.target.value);
    }
  };

  // 为文本输入类型添加 ref 和合成事件
  const getInputProps = () => {
    const baseProps = {
      ...fieldProps,
      onChange: onItemChange,
    };

    // 只为文本输入类型添加合成事件
    if (type === FieldType.INPUT || type === FieldType.TEXTAREA) {
      return {
        ...baseProps,
        ref: inputRef,
        onCompositionStart: onCompStart,
        // onCompositionEnd: onCompEnd,
      };
    }

    return baseProps;
  };

  return (
    <div className="field-item">
      <div className="label">{fieldProps.label}</div>
      <div className="field-content">{getFormItem(type, getInputProps())}</div>
    </div>
  );
};
