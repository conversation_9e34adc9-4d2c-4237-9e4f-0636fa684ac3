import './options.less';
import { Collapse } from 'antd';
import { SchemaType } from 'pb-form-render';
import type { Schema } from 'pb-form-render';
import type { FormItemSchema, RowSchema } from 'pb-form-render/dist/typing/types';
import { useEffect, useState } from 'react';

import { useRendererStore } from '@/store';
import type { RenderSchema } from '@/store/render';
import { getPathById, updateSchemas } from '@/utils/schema';

import { FieldCategory } from '../field/constant';
import { FieldItem } from '../field/fieldItem';
import { formatFields } from '../field/utils';
import { setValueByPath } from '../field/utils';

type ItemType = {
  key: string;
  label: string;
  children: React.ReactNode;
  style?: React.CSSProperties;
};

const specialProps = ['name', 'label', 'colNum'];

const OptionsWrapper = () => {
  const { activeId, schemas, getSchemas, getSchemaIds, setSchemas } =
    useRendererStore();
  const [fieldLists, setFieldLists] = useState<ItemType[]>();

  // let timeOut: unknown | null = null;
  // const dispatchChange = (type: string, value: any, item: Record<string, any>) => {
  //   if (timeOut) {
  //     clearTimeout(timeOut as number);
  //   }
  //   timeOut = setTimeout(() => {
  //     if (item.keyPath) {
  //       onChartFieldChange(value, item.keyPath);
  //     } else {
  //       onFieldChange(type, item.name, value);
  //     }
  //   }, 300);
  // };
  const onFieldChange = (value: any, keyName: string, type: FieldCategory) => {
    const schemaIds = getSchemaIds();
    const cSchemas = getSchemas();
    const idPath = getPathById(schemaIds, activeId);
    const currSchema =
      idPath.length > 1
        ? (cSchemas[idPath[0]] as RowSchema).children[idPath[1]]
        : cSchemas[idPath[0]];

    if (!currSchema) return;

    if (type === FieldCategory.PROPERTIES) {
      if (specialProps.includes(keyName)) {
        currSchema.properties[keyName as keyof Schema['properties']] = value;
      } else {
        (currSchema as FormItemSchema).properties.props[keyName] = value;
      }
    } else if (type === FieldCategory.RULES) {
      //
    }

    if (idPath.length > 1) {
      (cSchemas[idPath[0]] as RowSchema).children.splice(
        idPath[1],
        1,
        currSchema as FormItemSchema,
      );
    } else {
      cSchemas.splice(idPath[0], 1, currSchema as RenderSchema);
    }
    setSchemas([...cSchemas]);
    console.log(value, keyName, 'field change');
  };

  const generateFieldList = (fieldConfigs: ReturnType<typeof formatFields>) => {
    const list: ItemType[] = [];
    for (const key in fieldConfigs) {
      const listItem = {
        key: key,
        label: fieldConfigs[key as FieldCategory]?.title || '',
        children: (
          <div>
            {fieldConfigs[key as FieldCategory]!.children.map((child) => (
              <FieldItem
                key={child.name}
                fieldConfig={{
                  type: child.fieldType,
                  props: child.props,
                }}
                onChange={(value) => {
                  console.log(value, 'field change');
                  onFieldChange(value, child.name, key as FieldCategory);
                }}
              />
            ))}
          </div>
        ),
        style: {
          border: 'none',
          borderRadius: '5px',
        },
      };
      list.push(listItem);
    }

    return list;
  };

  useEffect(() => {
    const cSchema = getSchemas().find((item) => {
      if (item.type === SchemaType.ROW) {
        return item.children.find((child) => (child as RenderSchema).id === activeId);
      }
      return item.id === activeId;
    });
    console.log(cSchema, 'cshema');
    if (cSchema) {
      const fieldList = formatFields(cSchema);
      console.log(fieldList, 'fieldlist');
      setFieldLists(generateFieldList(fieldList));
    }
  }, [activeId, schemas]);

  // const configList = generateFieldList(fieldConfigs);

  return (
    <div className="options-wrapper">
      <Collapse items={fieldLists} bordered={false} />
    </div>
  );
};

export default OptionsWrapper;
